using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class SliderRelease : Mono<PERSON><PERSON><PERSON>our, IPointerUpHandler
{
    public float releaseDuration = 0.1f; 
    public AnimationCurve releaseCurve = AnimationCurve.Linear(0f, 0f, 1f, 1f);
    private Slider slider;
    public ParticleSystem[] particleSmake,particleSmake1;
    private float initialSliderValue;
    private float targetSliderValue;
    private Coroutine releaseCoroutine;
    public GameObject []effect,effect1;
    public Transform[] RL,RR;
    private void Start()
    {
        slider = GetComponent<Slider>(); 
        initialSliderValue = slider.value; 
        targetSliderValue = initialSliderValue;
    }
    public void Update()
    {
        if (slider.value > 300f)
        {
            effect[MainMenu.levlno].SetActive(true);
            effect1[MainMenu.levlno].SetActive(true);
            RL[MainMenu.levlno].GetComponent<wheelrotator>().speed = 50f;
            RR[MainMenu.levlno].GetComponent<wheelrotator>().speed = 50f;
            playermain.Instance.anim[MainMenu.levlno].GetComponent<Animator>().SetBool("tractor", true);
            playermain.Instance.anim[MainMenu.levlno].GetComponent<Animator>().SetBool("tractor1", false);
            playermain.Instance.aitractor[MainMenu.levlno].GetComponent<Animator>().SetBool("Aiup", false);
            playermain.Instance.aitractor[MainMenu.levlno].GetComponent<Animator>().SetBool("AIdown", true);
            foreach(var ps in particleSmake)
            {
               var mainModule = ps.GetComponent<ParticleSystem>().main;
               mainModule.startSize = 1.5f; // Reduced from 3f to 1.5f
            }
            foreach(var ps in particleSmake1)
            {
               var mainModule = ps.GetComponent<ParticleSystem>().main;
               mainModule.startSize = 0.4f; // Reduced from 0.7f to 0.4f
            }

        }
        if (slider.value < 300f)
        {
            playermain.Instance.anim[MainMenu.levlno].GetComponent<Animator>().SetBool("tractor1", true);
            playermain.Instance.anim[MainMenu.levlno].GetComponent<Animator>().SetBool("tractor", false);
            playermain.Instance.aitractor[MainMenu.levlno].GetComponent<Animator>().SetBool("Aiup", true);
            playermain.Instance.aitractor[MainMenu.levlno].GetComponent<Animator>().SetBool("AIdown", false);
            RL[MainMenu.levlno].GetComponent<wheelrotator>().speed = 500f;
            RR[MainMenu.levlno].GetComponent<wheelrotator>().speed = 500f;
             foreach(var ps in particleSmake)
            {
               var mainModule = ps.GetComponent<ParticleSystem>().main;
               mainModule.startSize = 0.4f; // Reduced from 0.7f to 0.4f
            }
            foreach(var ps in particleSmake1)
            {
               var mainModule = ps.GetComponent<ParticleSystem>().main;
               mainModule.startSize = 1.5f; // Reduced from 3f to 1.5f
            }
        }
    }
    public void OnPointerUp(PointerEventData eventData)
    {
        if (releaseCoroutine != null)
        {
            StopCoroutine(releaseCoroutine);
        }
        releaseCoroutine = StartCoroutine(ReleaseSlider());
    }
    private System.Collections.IEnumerator ReleaseSlider()
    {
        float elapsedTime = 0f;
        float startValue = slider.value;
        while (elapsedTime < releaseDuration)
        {
            float t = elapsedTime / releaseDuration;
            float curveValue = releaseCurve.Evaluate(t);
            slider.value = Mathf.Lerp(startValue, targetSliderValue, curveValue);
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        slider.value = targetSliderValue;
    }
}
