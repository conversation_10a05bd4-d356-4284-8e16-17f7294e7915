using UnityEngine;

public class WalkerCollisionHandler : MonoBehaviour
{
    public Animator walkerAnimator;
    public string deathTrigger = "isDead";

    private bool isDead = false;
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;

    void Start()
    {
        if (walkerAnimator == null) walkerAnimator = GetComponent<Animator>();
        FindWaypointWalkerAndIndex();
    }

    void OnTriggerEnter(Collider other)
    {
        if (!isDead && other.CompareTag("Player"))
            TriggerDeath();
    }

    void FindWaypointWalkerAndIndex()
    {
        waypointWalker = FindObjectOfType<WaypointWalker>();
        if (waypointWalker != null)
        {
            for (int i = 0; i < waypointWalker.spawnedPrefabs.Count; i++)
            {
                if (waypointWalker.spawnedPrefabs[i] == gameObject)
                {
                    walkerIndex = i;
                    break;
                }
            }
        }
    }

    void TriggerDeath()
    {
        isDead = true;
        if (walkerAnimator != null) walkerAnimator.SetTrigger(deathTrigger);
        if (waypointWalker != null && walkerIndex >= 0) waypointWalker.TriggerWalkerDeath(walkerIndex);
        GetComponent<Collider>().enabled = false;
    }
}
