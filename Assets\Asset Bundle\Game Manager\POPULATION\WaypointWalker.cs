using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WaypointWalker : MonoBehaviour
{
    [Header("Waypoint Settings")]
    public WPC waypointsContainer; // Reference to your WPC component
    public GameObject[] prefabToSpawn; // Assign your prefabs in the Inspector
    public float moveSpeed = 2f; // Speed of movement
    public int numberOfClones = 5; // Number of clones to generate

    [Header("Detection Settings")]
    public Transform player;
    public float activationRange = 50f;
    public float playerStopRadius = 15f;
    public float walkerStopDistance = 5f;

    [Header("Animation Settings")]
    public string walkAnimationParameter = "isWalking"; // Bool parameter for walking animation
    public string idleAnimationParameter = "isIdle"; // Bool parameter for idle animation
    public string deathAnimationParameter = "isDead"; // Trigger parameter for death animation

    // Lists for managing spawned prefabs
    [System.NonSerialized] public List<GameObject> spawnedPrefabs = new List<GameObject>(); // Public for collision handler verification
    private List<int> currentWaypointIndices = new List<int>();
    private List<int> waypointDirections = new List<int>(); // 1 for forward, -1 for backward
    private List<Animator> spawnedAnimators = new List<Animator>(); // Animators for each spawned prefab
    private List<bool> walkerDeathStates = new List<bool>(); // Track death state for each walker
    private static GameObject parentObject; // Static parent object for organizing all prefabs

    private void Start()
    {
        if (waypointsContainer.waypoints.Count == 0)
        {
            Debug.LogWarning("No waypoints found. Please assign waypoints in the inspector.");
            return;
        }

        if (prefabToSpawn.Length == 0)
        {
            Debug.LogWarning("No prefabs assigned. Please assign prefabs in the inspector.");
            return;
        }

        // Create the parent object if it doesn't exist yet
        if (parentObject == null)
        {
            parentObject = new GameObject("SpawnedPrefabsParent");
        }

        int waypointsCount = waypointsContainer.waypoints.Count;
        int prefabsCount = prefabToSpawn.Length;
        int clonesToSpawn = Mathf.Min(numberOfClones, prefabsCount * waypointsCount);

        for (int i = 0; i < clonesToSpawn; i++)
        {
            int prefabIndex = i % prefabsCount; // Cycle through prefabs
            int waypointIndex = i % waypointsCount; // Cycle through waypoints
            SpawnPrefabAtWaypoint(prefabIndex, waypointIndex);
            currentWaypointIndices.Add(waypointIndex);
            waypointDirections.Add(1); // Start by moving forward
            walkerDeathStates.Add(false); // Initialize as alive
        }
    }

    private void SpawnPrefabAtWaypoint(int prefabIndex, int waypointIndex)
    {
        Vector3 spawnPosition = waypointsContainer.waypoints[waypointIndex].transform.position;
        GameObject spawnedPrefab = Instantiate(prefabToSpawn[prefabIndex], spawnPosition, Quaternion.identity);

        // Set the prefab as a child of the single parent object
        spawnedPrefab.transform.parent = parentObject.transform;

        // Get the Animator component from the spawned prefab
        Animator animator = spawnedPrefab.GetComponent<Animator>();
        if (animator == null)
        {
            // Try to get animator from children if not found on root
            animator = spawnedPrefab.GetComponentInChildren<Animator>();
        }

        // Add trigger collider for collision detection
        if (spawnedPrefab.GetComponent<Collider>() == null)
        {
            // Add a capsule trigger collider for precise collision detection
            CapsuleCollider triggerCollider = spawnedPrefab.AddComponent<CapsuleCollider>();
            triggerCollider.height = 1.6f; // Human height
            triggerCollider.radius = 0.25f; // Smaller radius for precise hit detection
            triggerCollider.center = new Vector3(0, 0.8f, 0); // Center at waist level
            triggerCollider.isTrigger = true; // Use trigger for immediate detection
        }
        else
        {
            // Make sure existing collider is set as trigger and adjust size
            Collider existingCollider = spawnedPrefab.GetComponent<Collider>();
            existingCollider.isTrigger = true;

            // If it's a capsule collider, adjust the size for precise detection
            if (existingCollider is CapsuleCollider capsule)
            {
                capsule.radius = 0.25f; // Smaller radius for precise hit detection
                capsule.height = 1.6f;
                capsule.center = new Vector3(0, 0.8f, 0);
            }
        }

        // Add rigidbody if not present (needed for collision detection)
        if (spawnedPrefab.GetComponent<Rigidbody>() == null)
        {
            Rigidbody rb = spawnedPrefab.AddComponent<Rigidbody>();
            rb.isKinematic = true; // Kinematic so it doesn't fall due to gravity
            rb.useGravity = false;
        }

        // Add to lists first to get correct index
        spawnedPrefabs.Add(spawnedPrefab);
        spawnedAnimators.Add(animator); // Add animator to list (can be null if not found)

        // Get the correct walker index (current count - 1 since we just added it)
        int walkerIndex = spawnedPrefabs.Count - 1;

        // Add the collision handler component for immediate trigger detection
        WalkerCollisionHandler collisionHandler = spawnedPrefab.GetComponent<WalkerCollisionHandler>();
        if (collisionHandler == null)
        {
            collisionHandler = spawnedPrefab.AddComponent<WalkerCollisionHandler>();
        }

        // Set up the collision handler with proper references
        collisionHandler.walkerAnimator = animator;
        collisionHandler.deathTrigger = deathAnimationParameter;


    }

    /// <summary>
    /// Gets the current player position from RCC Scene Manager or fallback to assigned player transform
    /// </summary>
    private Vector3 GetPlayerPosition()
    {
        // Try to get player position from RCC Scene Manager first
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            return RCC_SceneManager.Instance.activePlayerVehicle.transform.position;
        }

        // Fallback to manually assigned player transform
        if (player != null)
        {
            return player.position;
        }

        // Return zero if no player found
        return Vector3.zero;
    }

    private bool IsPlayerNearWalker(Transform walkerTransform)
    {
        Vector3 playerPos = GetPlayerPosition();
        if (playerPos == Vector3.zero) return false;

        Vector3 walkerFront = walkerTransform.position + walkerTransform.forward * 0.5f;
        float distance = Vector3.Distance(playerPos, walkerFront);
        Vector3 directionToPlayer = (playerPos - walkerTransform.position).normalized;
        float dotProduct = Vector3.Dot(walkerTransform.forward, directionToPlayer);

        return distance <= playerStopRadius && dotProduct > 0.3f;
    }

    private bool IsWalkerTooClose(int currentIndex)
    {
        GameObject current = spawnedPrefabs[currentIndex];
        Vector3 currentPos = current.transform.position;
        Vector3 currentForward = current.transform.forward;

        for (int i = 0; i < spawnedPrefabs.Count; i++)
        {
            if (i == currentIndex || IsWalkerDead(i)) continue;
            GameObject other = spawnedPrefabs[i];
            if (other == null || !other.activeInHierarchy) continue;

            Vector3 directionToOther = (other.transform.position - currentPos).normalized;
            float dotProduct = Vector3.Dot(currentForward, directionToOther);

            if (dotProduct > 0.5f && Vector3.Distance(currentPos, other.transform.position) <= walkerStopDistance)
                return true;
        }
        return false;
    }

    /// <summary>
    /// Checks if player vehicle exists and is active
    /// </summary>
    private bool IsPlayerVehicleActive()
    {
        return (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null) || player != null;
    }

    private void SetWalkingAnimation(int walkerIndex, bool isWalking)
    {
        if (walkerIndex < 0 || walkerIndex >= spawnedAnimators.Count) return;

        Animator animator = spawnedAnimators[walkerIndex];
        if (animator == null) return;

        if (!string.IsNullOrEmpty(walkAnimationParameter))
            animator.SetBool(walkAnimationParameter, isWalking);
        if (!string.IsNullOrEmpty(idleAnimationParameter))
            animator.SetBool(idleAnimationParameter, !isWalking);
    }

    /// <summary>
    /// Triggers the death animation for a walker (called by collision handler)
    /// </summary>
    public void TriggerWalkerDeath(int walkerIndex)
    {
        if (walkerIndex >= 0 && walkerIndex < spawnedPrefabs.Count && walkerIndex < walkerDeathStates.Count && walkerDeathStates[walkerIndex])
            walkerDeathStates[walkerIndex] = false;
        TriggerDeathAnimation(walkerIndex);
    }

    /// <summary>
    /// Internal method to trigger the death animation for a walker
    /// </summary>
    private void TriggerDeathAnimation(int walkerIndex)
    {
        if (walkerIndex < 0 || walkerIndex >= spawnedAnimators.Count || walkerIndex >= walkerDeathStates.Count) return;
        if (walkerDeathStates[walkerIndex]) return;

        Animator animator = spawnedAnimators[walkerIndex];
        if (animator == null) return;

        walkerDeathStates[walkerIndex] = true;
        SetWalkingAnimation(walkerIndex, false);

        if (!string.IsNullOrEmpty(deathAnimationParameter))
            animator.SetTrigger(deathAnimationParameter);
    }

    /// <summary>
    /// Checks if a walker is dead
    /// </summary>
    private bool IsWalkerDead(int walkerIndex)
    {
        if (walkerIndex < 0 || walkerIndex >= walkerDeathStates.Count) return false;
        return walkerDeathStates[walkerIndex];
    }

    /// <summary>
    /// Reset walker death state (for testing/debugging)
    /// </summary>


    private void Update()
    {
        if (waypointsContainer.waypoints.Count == 0)
        {
            Debug.LogWarning("No waypoints found.");
            return;
        }

        // Get player position once per frame for efficiency
        Vector3 playerPosition = GetPlayerPosition();
        bool playerExists = IsPlayerVehicleActive();

        for (int i = 0; i < spawnedPrefabs.Count; i++)
        {
            GameObject spawnedPrefab = spawnedPrefabs[i];
            int currentWaypointIndex = currentWaypointIndices[i];
            int direction = waypointDirections[i];

            if (spawnedPrefab == null) continue;

            float distanceToPlayer = playerExists ? Vector3.Distance(playerPosition, spawnedPrefab.transform.position) : float.MaxValue;

            // Activate or deactivate prefab based on player range (both living and dead)
            if (distanceToPlayer <= activationRange)
            {
                if (!spawnedPrefab.activeInHierarchy)
                {
                    spawnedPrefab.SetActive(true);
                }
            }
            else
            {
                if (spawnedPrefab.activeInHierarchy)
                {
                    spawnedPrefab.SetActive(false);
                }
                continue; // Skip movement if the prefab is inactive
            }

            // Skip movement and animation processing if walker is dead
            if (IsWalkerDead(i))
            {
                continue;
            }

            bool playerNearby = IsPlayerNearWalker(spawnedPrefab.transform);
            bool walkerTooClose = IsWalkerTooClose(i);

            if (playerNearby || walkerTooClose)
            {
                SetWalkingAnimation(i, false);
                continue;
            }

            SetWalkingAnimation(i, true);

            Transform currentWaypoint = waypointsContainer.waypoints[currentWaypointIndex].transform;
            Vector3 directionVector = currentWaypoint.position - spawnedPrefab.transform.position;

            Vector3 combinedMovement = directionVector.normalized;

            if (directionVector.magnitude < 0.1f)
            {
                if (currentWaypointIndex == 0 && direction == -1)
                {
                    direction = 1;
                }
                else if (currentWaypointIndex == waypointsContainer.waypoints.Count - 1 && direction == 1)
                {
                    direction = -1;

                    // Stop walking animation when reaching end
                    SetWalkingAnimation(i, false);

                    // Deactivate the prefab when it reaches the last waypoint
                    spawnedPrefab.SetActive(false);

                    // Reactivate the prefab at the first waypoint after a delay
                    StartCoroutine(ReactivateAtFirstWaypoint(spawnedPrefab, i));
                    continue; // Skip further processing for this prefab
                }
                currentWaypointIndex += direction;
            }

            spawnedPrefab.transform.Translate(combinedMovement * moveSpeed * Time.deltaTime, Space.World);
            Quaternion targetRotation = Quaternion.LookRotation(directionVector);
            spawnedPrefab.transform.rotation = Quaternion.Slerp(spawnedPrefab.transform.rotation, targetRotation, Time.deltaTime * moveSpeed);

            currentWaypointIndices[i] = currentWaypointIndex;
            waypointDirections[i] = direction;
        }
    }

    private IEnumerator ReactivateAtFirstWaypoint(GameObject prefab, int walkerIndex)
    {
        // Reactivate and move the prefab to the first waypoint
        int firstWaypointIndex = 0;
        Vector3 firstWaypointPosition = waypointsContainer.waypoints[firstWaypointIndex].transform.position;

        // Move the prefab to the first waypoint immediately
        prefab.transform.position = firstWaypointPosition;
        prefab.transform.rotation = Quaternion.identity; // Reset rotation or set it to a specific direction if needed
        prefab.SetActive(true);

        // Reset the prefab's state
        currentWaypointIndices[walkerIndex] = firstWaypointIndex;
        waypointDirections[walkerIndex] = 1; // Set direction to move forward from the first waypoint

        // Reset animation state to idle initially
        SetWalkingAnimation(walkerIndex, false);

        // Correct the prefab's rotation to face the next waypoint
        if (waypointsContainer.waypoints.Count > 1)
        {
            Transform nextWaypoint = waypointsContainer.waypoints[1].transform;
            Vector3 directionVector = nextWaypoint.position - prefab.transform.position;
            if (directionVector != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(directionVector);
                prefab.transform.rotation = targetRotation;
            }
        }

        yield return null; // Allow one frame for the prefab to be activated and start moving
    }


}
